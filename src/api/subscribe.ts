/* eslint-disable max-len */
import { app } from '@tencent/tea-app';
import { getLanguageParam } from '@/utils/i18n';
import {
  SubscriptionInfos,
  Languages,
  SubscriptionParams,
  SubscriptionTpl,
  IDescribeRiskManageSubjectListParams,
} from '@/interfaces/subscribe';

// 模板列表 DescribeSubscriptionTemplates
// eslint-disable-next-line max-len
export const DescribeSubscriptionTemplates = (data: SubscriptionParams): Promise<SubscriptionInfos> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeSubscriptionTemplateV2',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 创建订阅模版 CreateSubscriptionTemplate
// eslint-disable-next-line max-len
export const CreateSubscriptionTemplate = (data: SubscriptionTpl): Promise<SubscriptionInfos> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'CreateSubscriptionTemplateV2',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 更新订阅模板 ModifySubscriptionTemplate
// eslint-disable-next-line max-len
export const ModifySubscriptionTemplate = (data: SubscriptionTpl): Promise<SubscriptionInfos> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'ModifySubscriptionTemplateV2',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 删除订阅模板 DeleteSubscriptionTemplate
// eslint-disable-next-line max-len
export const DeleteSubscriptionTemplate = (data: { Ids: Array<number> }): Promise<SubscriptionInfos> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DeleteSubscriptionTemplate',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 描述订阅信息
// eslint-disable-next-line max-len
export const getSubscriptions = (data: SubscriptionParams): Promise<SubscriptionInfos> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeSubscriptions',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 更新、新建订阅信息
export const updateSubscription = (data: SubscriptionInfos): Promise<any> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'UpdateSubscriptionV2',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取产品、维度配置信息
export const getProductsGroups = (): Promise<any> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeGroupAndProductInfos',
        data: {
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 删除订阅  DeleteSubscription
export const deleteSubscription = (data: { Ids: Array<number> }): Promise<any> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DeleteSubscription',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取插件架构图列表
export const DescribePluginArchList = (data?: any): Promise<any> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeReportArchList',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取架构图的SVG图
export const DescribeArchSvg = (data?: any): Promise<any> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeArchSvgData',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取支持语言
export const getSupportLanguages = (): Promise<Languages> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeSupportLanguage',
        data: {
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取报告订阅邮箱列表
export const DescribeSubscriptionEmailList = (data: {
  Email?: string,
  Limit: number,
  Offset: number
}): Promise<Languages> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeSubscriptionEmailList',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 删除报告订阅邮箱
export const DeleteSubscriptionEmail = (data: {
  Email: string
}): Promise<Languages> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DeleteSubscriptionEmail',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 添加报告订阅邮箱
export const CreateSubscriptionEmail = (data: {
  Email: string
}): Promise<Languages> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'CreateSubscriptionEmail',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取报告架构图列表
export const DescribeReportArchList = (data: {
  ResourceBindStatus: boolean,
  TotalFlag: boolean,
  PageNumber: number,
  PageSize: number
}): Promise<Languages> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeReportArchList',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 获取报风险主题列表
export const getRiskManageSubjectList = (data: IDescribeRiskManageSubjectListParams): Promise<Languages> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeRiskManageSubjectList',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});
