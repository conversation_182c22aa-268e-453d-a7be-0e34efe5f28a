import { app } from '@tencent/tea-app';
import { getLanguageParam } from '@/utils/i18n';
import {
  IngoreStrategy,
  Filters,
  GlobalTags,
  Tags,
  ResTagKeys,
  ResTagValues,
  Condition,
  ModifyIgnoreStatusParams,
  DescribeIgnoredInstancesParams,
} from '@/interfaces/switch';

const isDev = process.env.NODE_ENV === 'development';
const basePath = `${isDev ? '' : ''}`;

export const getIngoreStrategyIdList = (): Promise<IngoreStrategy> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'ListIgnoreStrategies',
        data: {
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// eslint-disable-next-line max-len
export const updateIngoreStrategies = (data: { StrategyId: number, Operate: string }) => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'ModifyIgnoreStrategies',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// eslint-disable-next-line max-len
export const getGlobalIgnoreTags = (data: { TaskId?: string, Filters?: Filters, Choose: number }): Promise<GlobalTags> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeGlobalIgnoreTags',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

export const deleteGlobalIgnoreTags = (data: { TagKeys: Array<string> }) => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DeleteGlobalIgnoreTags',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

export const modifyGlobalIgnoreTags = (data: { Tags: Array<Tags> }) => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'ModifyGlobalIgnoreTags',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 新增 DescribeTagKeys 接口，拉取标签键
// eslint-disable-next-line max-len
export const getTagKeys = (data: { Offset: number, Limit: number }): Promise<ResTagKeys> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'tag',
        cmd: 'DescribeTagKeys',
        data: {
          ...data,
          Version: '2018-08-13',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// 新增 DescribeTagValues 接口，拉取标签值列表
// eslint-disable-next-line max-len
export const getTagValues = (data: { Offset: number, Limit: number, TagKeys: Array<string> }): Promise<ResTagValues> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'tag',
        cmd: 'DescribeTagValues',
        data: {
          ...data,
          Version: '2018-08-13',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// DescribeCustomThresholdCondition
export const DescribeCustomThresholdCondition = (data): Promise<Condition> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'DescribeCustomThresholdCondition',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// ModifyCustomThresholdCondition
// eslint-disable-next-line max-len
export const ModifyCustomThresholdCondition = (data): Promise<{ [propName: string]: any }> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: 'ModifyCustomThresholdCondition',
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// eslint-disable-next-line max-len
export const ModifyIgnoreStatus = (data: ModifyIgnoreStatusParams): Promise<{ [propName: string]: any }> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: `${basePath}ModifyIgnoreStatus`,
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

// eslint-disable-next-line max-len
export const DescribeIgnoredInstances = (data: DescribeIgnoredInstancesParams): Promise<{ [propName: string]: any }> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: `${basePath}DescribeIgnoredInstances`,
        data: {
          ...data,
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

export const DescribeFuzzyIgnoreInfo = (): Promise<{ [propName: string]: any }> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: `${basePath}DescribeFuzzyIgnoreInfo`,
        data: {
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});

export const DescribeFuzzyIgnoreConfig = (): Promise<{ [propName: string]: any }> => new Promise((resolve, reject) => {
  app.capi
    .requestV3(
      {
        serviceType: 'advisor',
        cmd: `${basePath}DescribeFuzzyIgnoreConfig`,
        data: {
          Version: '2020-07-21',
          Language: getLanguageParam(),
        },
        regionId: 1,
      },
      {
        tipErr: false,
      },
    )
    .then((data) => {
      resolve(data.data.Response);
    })
    .catch(async (err) => {
      if (err?.code === 'AuthFailure.UnauthorizedOperation') {
        const cam = await app.sdk.use('cam-sdk');
        cam.showBreakModal({ message: err?.data?.message });
      } else {
        reject(err);
      }
    });
});
