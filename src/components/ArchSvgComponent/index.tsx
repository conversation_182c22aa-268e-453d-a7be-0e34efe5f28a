import React, { useRef, useEffect } from 'react';
import { getValidString } from '@src/utils/index';
import { BLANK_SVG } from '@src/constants/acth';
import s from './index.module.scss';

interface IArchSvgComponentProps {
  detail: string;
}

const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

// 历史原因导致生成了一些架构图的svg网格路径只有2000，所以对这些脏数据进行兼容显示
const points = '-2000 -2000, 2000 -2000, 2000 2000, -2000 2000';

export default function ArchSvgComponent(props: IArchSvgComponentProps): React.ReactElement {
  const { detail } = props;

  const svgContainer = useRef<HTMLDivElement>(null);

  const changePoints = (points: string, viewBoxArray: string[]) => {
    const [viewBoxX, viewBoxY, viewBoxWidth, viewBoxHeight] = viewBoxArray;
    // 将 points 属性的值拆分为坐标数组
    const coordinates1 = points.split(',');
    const newCoordinates1 = coordinates1.map((coordinate) => {
      const values = coordinate.trim().split(' ');
      const updatedValues = values.map((value, index) => {
        const parsedValue = Number(value);
        if (index === 0 && parsedValue > 0) {
          return (Math.abs(Number(viewBoxX)) + Math.abs(Number(viewBoxWidth))) + parsedValue;
        } if (index === 0 && parsedValue < 0) {
          return parsedValue - (Math.abs(Number(viewBoxX)) + Math.abs(Number(viewBoxWidth)));
        } if (index === 1 && parsedValue > 0) {
          return (Math.abs(Number(viewBoxY)) + Math.abs(Number(viewBoxHeight))) + parsedValue;
        } if (index === 1 && parsedValue < 0) {
          return parsedValue - (Math.abs(Number(viewBoxY)) + Math.abs(Number(viewBoxHeight)));
        }
        return Math.abs(Number(viewBoxY)) + Math.abs(Number(viewBoxHeight)) + parsedValue;
      });
      return updatedValues.join(' ');
    });

    // 将修改后的坐标数组重新组合为新的 points 属性值
    const newPoints = newCoordinates1.join(', ');
    return newPoints;
  };

  const fixSafariSvgText = () => {
    const svg = svgContainer.current.querySelector('svg');
    const foreignObjectArray = svg.querySelectorAll('foreignObject');
    foreignObjectArray.forEach((foreignObject: any) => {
      // eslint-disable-next-line no-param-reassign
      foreignObject.querySelector('div').style.position = '';
      // eslint-disable-next-line no-param-reassign
      foreignObject.querySelector('div').style.top = '';
    });
  };

  useEffect(() => {
    if (svgContainer.current && detail) {
      try {
        const svg = svgContainer.current.querySelector('svg');
        const viewBox = svg.getAttribute('viewBox');
        const viewBoxArray = viewBox.split(' ');
        const sence = svg.querySelector('g[g-name="sence"]');
        const grid = svg.querySelector('g[g-name="grid"]');
        const polygon1: any = sence.querySelector('polygon');
        const polygon2 = grid.querySelector('polygon');
        const points1 = polygon1.getAttribute('points');
        const points2 = polygon2.getAttribute('points');
        if (points1 === points) {
          const newPoints1 = changePoints(points1, viewBoxArray);
          polygon1.setAttribute('points', newPoints1);
        }
        if (points2 === points) {
          const newPoints2 = changePoints(points2, viewBoxArray);
          polygon2.setAttribute('points', newPoints2);
        }

        if (isSafari) {
          fixSafariSvgText();
        }
      } catch (error) {
        console.error(error);
      }
    }
  }, [detail]);

  return (
    <div
      ref={svgContainer}
      className={s['arch-svg-component']}
    >
      <div
        className={s['arch-svg']}
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{
          __html: getValidString(detail || BLANK_SVG),
        }}
      />
    </div>
  );
}
