// NOCA:arrow-parens
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Segment,
  SelectMultiple,
  Radio,
  TimePicker,
  InputAdornment,
  Button,
  Checkbox,
  TagSearchBox,
  Table,
  StatusTip,
  message,
  notification,
} from '@tencent/tea-component';
import moment from 'moment';
import { useField, useForm } from 'react-final-form-hooks';
import { t, Trans } from '@tea/app/i18n';
import {
  updateSubscription,
  DescribeArchSvg,
  DescribeSubscriptionEmailList,
  getProductsGroups,
  DescribeReportArchList,
} from '@src/api/subscribe';
import { getTagKeys, getTagValues } from '@src/api/switch';
import { app } from '@tencent/tea-app';
import { TagSon } from '@/interfaces/switch';
import { cloneDeep } from 'lodash';
import { FREQUENCY_OPTS, WEEK_OPTS, MONTH_OPTS } from '@/constants/config';
import ArchSvgComponent from '../ArchSvgComponent';

import './style.scss';

export interface SubModalProps {
  subModalInfo: any;
  environment?: 'advisor' | 'cloud-arch';
  onClose: (refresh: any) => void;
}

enum pluginNameEnum {
  cloudInspectionSdk = 'cloud-inspection-sdk',
  capacityMonitoringSdk = 'capacity-monitoring-sdk'
}

const pluginNameTabMap = {
  [pluginNameEnum.cloudInspectionSdk]: '1',
  [pluginNameEnum.capacityMonitoringSdk]: '2',
};

function getStatus(meta, validating) {
  if (meta.active && validating) {
    return 'validating';
  }
  if (!meta.touched) {
    return null;
  }
  return meta.error ? 'error' : 'success';
}

export default function SubscriptionPopup({
  subModalInfo,
  environment,
  onClose,
}: SubModalProps) {
  // 标签键列表
  const [tagKeys, setTagKeys] = useState<Array<string>>([]);
  // 标签值列表
  const [tagValues, setTagValues] = useState<Array<TagSon>>([]);
  const [attributes, setAttributes] = useState<any>([]);
  const [svgList, setSvgList] = useState([]);
  const [changeSvgType, setChangeSvgType] = useState('select');
  const { pageable, selectable } = Table.addons;
  const [emaiRecordlList, setEmailRecordList] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(8);
  const [pageIndex, setPageIndex] = useState(0);
  const [emailLoading, setEmailLoading] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [clicked, setClicked] = useState(false);
  const [tagVal, setTagVal] = useState([]);
  const [saveLoading, setSaveLoading] = useState(false);
  const [isCloseRefresh, setIsCloseRefresh] = useState(false);
  // 维度列表
  const [groups, setGroups] = useState([]);
  // 产品列表
  const [products, setProducts] = useState([]);
  const [archList, setArchList] = useState([]);

  const emailTitlePre = t('腾讯云顾问（Advisor）');
  const tabVal = 'emailManagement';
  const subscribePath = '/advisor/subscribe';

  const onSubmit = async (value) => {
    if (selectedKeys?.length === 0) {
      return;
    }
    setSaveLoading(true);
    let params: any = {
      Id: -1,
      IsSubscribed: true,
      TimeInfo: {
        Period: value.Period,
        Time: value.Time.format('HH:00'),
        Monthly: value.Monthly ? value.Monthly.join(',') : '',
        DaysOfWeek: value.DaysOfWeek ? value.DaysOfWeek.join(',') : '',
      },
      EmailInfo: {
        EmailAddrs: selectedKeys,
      },
      Name: value.Name,
      EmailTitle: value.EmailTitle,
      ReportType: value.ReportType,
      IsHideCusName: value.IsHideCusName,
    };
    let obj;
    if (value.ReportType === '1' || value.ReportType === '2') {
      obj = {
        ArchIds: value.ArchIds,
      };
    } else {
      obj = {
        RiskLevel: parseInt(value.RiskLevel, 10),
        GroupIds: value.GroupIds.map((item) => parseInt(item, 10)),
        Products: value.Products.length === products.length ? [] : value.Products,
        Tags: value.Tags,
      };
    }
    params = {
      ...params,
      ...obj,
    };
    params.EmailTitle = `${emailTitlePre}${params.EmailTitle}`;
    if (subModalInfo.subInfo) {
      if (subModalInfo.subInfo.Id !== undefined) {
        params.Id = subModalInfo.subInfo.Id;
        params.TplId = subModalInfo.subInfo.TplId;
        params.TplName = subModalInfo.subInfo.TplName;
        params.SubscriptionUserInfo = subModalInfo.subInfo.SubscriptionUserInfo;
        params.SubUserStatus = subModalInfo.subInfo.SubUserStatus;
        params.IsSubscribed = subModalInfo.subInfo.IsSubscribed;
      } else {
        params.IsSubscribed = true;
      }
    }
    try {
      const res = await updateSubscription({
        SubscriptionInfos: [
          {
            ...params,
          },
        ],
      });
      if (res.Error) {
        setSaveLoading(false);
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { Message } = res.Error;
        app.tips.error(t('{{Message}}', { Message }));
        return;
      }
      notification.success({
        description: environment === 'cloud-arch'
          // @ts-ignore
          // eslint-disable-next-line react/jsx-one-expression-per-line
          ? <Trans>订阅成功，可点击<a href="/advisor/subscribe" target="_blank">前往报告订阅</a>进行管理</Trans>
          : t('操作成功'),
      });
      onClose(true);
      setSaveLoading(false);
    } catch (err) {
      setSaveLoading(false);
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const Message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{Message}}', { Message }));
    }
  };

  const initialParams = {
    Id: -1,
    IsSubscribed: true,
    GroupIds: groups?.map((item) => item.Id.toString()),
    Tags: [],
    Name: '',
    Period: 'Daily',
    Time: moment('17:00', 'HH:00'),
    ReportType: pluginNameTabMap[subModalInfo?.pluginName] ?? '1',
    Monthly: [],
    DaysOfWeek: [],
    EmailTitle: '',
    ArchIds: [],
    RiskLevel: '0',
    Products: [],
    IsHideCusName: false,
  };

  const {
    form,
    handleSubmit,
    // @ts-ignore
    values,
    // @ts-ignore
    validating,
  } = useForm<any>({
    // @ts-ignore
    onSubmit: (value) => onSubmit(value),
    initialValuesEqual: () => true,
    initialValues: {
      ...initialParams,
    },
    validate: (formInfo) => {
      let validateFormInfo: any = {};
      if (formInfo.ReportType === '1' || formInfo.ReportType === '2') {
        validateFormInfo = {
          ArchIds: ((!formInfo.ArchIds) || formInfo.ArchIds?.length === 0) ? t('请选择架构名称') : undefined,
          Name: !formInfo.Name ? t('请输入订阅名称') : undefined,
          EmailTitle: !formInfo.EmailTitle ? t('请输入邮件标题') : undefined,
        };
      } else {
        validateFormInfo = {
          GroupIds: ((!formInfo.GroupIds) || formInfo.GroupIds?.length === 0) ? t('请选择类别') : undefined,
          Name: !formInfo.Name ? t('请输入订阅名称') : undefined,
          EmailTitle: !formInfo.EmailTitle ? t('请输入邮件标题') : undefined,
          RiskLevel: !formInfo.RiskLevel ? t('请选择风险级别') : undefined,
        };
      }
      if (formInfo.Period === 'Weekly') {
        // eslint-disable-next-line max-len
        validateFormInfo.DaysOfWeek = ((!formInfo.DaysOfWeek) || formInfo.DaysOfWeek?.length === 0) ? t('请选择天数') : undefined;
      }
      if (formInfo.Period === 'Monthly') {
        validateFormInfo.Monthly = ((!formInfo.Monthly) || formInfo.Monthly?.length === 0) ? t('请选择日期') : undefined;
      }
      return validateFormInfo;
    },
  });
  const archIdsField = useField('ArchIds', form);
  const riskLevelField = useField('RiskLevel', form);
  const emailTitleField = useField('EmailTitle', form);
  const productslField = useField('Products', form);
  const groupIdslField = useField('GroupIds', form);
  const nameField = useField('Name', form);
  const tagsField = useField('Tags', form);
  const isHideCusNameField = useField('IsHideCusName', form);
  const periodField = useField('Period', form);
  const monthlyField = useField('Monthly', form);
  const daysOfWeekField = useField('DaysOfWeek', form);
  const timeField = useField('Time', form);
  const reportTypeField = useField('ReportType', form);
  // 获取标签键清单 注意需要循环拉取全量标签键
  const getAllTagKeys = async () => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const Limit = 100;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    let Offset = 0;
    let tags = [];
    try {
      // 首次拉取
      const res = await getTagKeys({ Offset, Limit });
      // 判断报错
      if (res.Error) {
        const msg = res.Error.Message || t('未知错误');
        message.error({ content: msg });
        return;
      }
      // 更新结果
      tags = tags.concat(res.Tags);
      // 循环拉取剩余标签键
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { TotalCount } = res;
      for (let i = 1; i < TotalCount / Limit; i++) {
        Offset = i * Limit;
        // eslint-disable-next-line no-await-in-loop
        const res1 = await getTagKeys({ Offset, Limit });
        if (res1.Error) {
          const msg = res1.Error.Message || t('未知错误');
          message.error({ content: msg });
          return;
        }
        // 更新结果
        tags = tags.concat(res1.Tags);
      }
      setTagKeys(tags);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  const columns = [
    {
      key: 'Email',
      header: '',
      render(item) {
        return <>
          <div className="email-line">
            <div className="label-t">{t('用户名：')}</div>
            <div className="label-con">{item.UserName}</div>
          </div>
          <div className="email-line">
            <div className="label-t">{t('邮箱：')}</div>
            <div className="label-con">{item.Email}</div>
          </div>
        </>;
      },
    },
    {
      key: 'select',
      header: '',
      width: 50,
    },
  ];

  // 获取标签值列表 注意需要循环拉取全量标签值
  const getAllTagValues = async (tagKey) => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const Limit = 100;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    let Offset = 0;
    let tags = []; // 注意这个返回格式是 {{TagKey:'xxx',TagValue:'xxxx'}]
    try {
      // 首次拉取
      const res = await getTagValues({ Offset, Limit, TagKeys: tagKey });
      // 判断报错
      if (res.Error) {
        const msg = res.Error.Message || t('未知错误');
        message.error({ content: msg });
        return;
      }
      // 更新结果
      tags = tags.concat(res.Tags);
      // 循环拉取剩余标签键
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { TotalCount } = res;
      for (let i = 1; i < TotalCount / Limit; i++) {
        Offset = i * Limit;
        // eslint-disable-next-line no-await-in-loop
        const res1 = await getTagValues({ Offset, Limit, TagKeys: tagKey });
        if (res1.Error) {
          const msg = res1.Error.Message || t('未知错误');
          message.error({ content: msg });
          return;
        }
        // 更新结果
        tags = tags.concat(res1.Tags);
      }
      // 转换成选择项
      setTagValues(tags);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      message.error({ content: msg });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-misused-promises, no-async-promise-executor
  const getDescribeArchSvg = (val) => new Promise(async (resolve) => {
    try {
      const res = await DescribeArchSvg({
        ArchId: val,
        IsThreeDimension: true,
      });
      if (res.Error) {
        const message = res.Error.Message || '';
        app.tips.error(message);
        return;
      }
      const scgStr = res?.Svg;
      // eslint-disable-next-line no-nested-ternary
      const str = scgStr ? scgStr?.indexOf('\'') === 0 ? scgStr.slice(1, scgStr.length - 1) : scgStr : '';
      resolve({
        archId: val,
        svg: str,
      });
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  });

  const getDescribeSubscriptionEmailList = async () => {
    setEmailLoading(true);
    try {
      const res = await DescribeSubscriptionEmailList({
        Limit: limit,
        Offset: pageIndex * limit,
      });
      if (res.Error) {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { Message } = res.Error;
        app.tips.error(t('{{Message}}', { Message }));
        setEmailLoading(false);
        return;
      }
      setEmailRecordList(res.EmailDetails || []);
      setTotalCount(res.TotalCount);
      setEmailLoading(false);
    } catch (err) {
      setEmailLoading(false);
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const Message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{Message}}', { Message }));
    }
  };

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res = await getProductsGroups();
      if (res.Error) {
        const msg = res.Error.Message;
        app.tips.error(t('{{msg}}', { msg }));
        return;
      }
      setGroups(res.Groups);
      setProducts(res.Products);
      if ((Array.isArray(subModalInfo.subInfo?.Products) && subModalInfo.subInfo?.Products.length === 0)
          || !subModalInfo.subInfo?.Products) {
        form.change('Products', res.Products.map((item) => item.Product));
      }
      if (!subModalInfo.subInfo) {
        form.change('GroupIds', res.Groups?.map((item) => item.Id.toString()));
      }
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };

  const getDescribeReportArchList = async () => {
    try {
      const res = await DescribeReportArchList({
        ResourceBindStatus: true,
        TotalFlag: true,
        PageNumber: 0,
        PageSize: 0,
      });
      if (res.Error) {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { Message } = res.Error;
        app.tips.error(t('{{Message}}', { Message }));
        return;
      }
      setArchList(res.ArchList);
    } catch (err) {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const Message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{Message}}', { Message }));
    }
  };

  const handleReportTypeChange = (value: '1' | '0' | '2') => {
    if (values.ArchIds?.length === 0) {
      form.resetFieldState('ArchIds');
    }
    form.resetFieldState('EmailTitle');
    form.resetFieldState('Name');
    form.resetFieldState('DaysOfWeek');
    form.resetFieldState('Monthly');
    form.resetFieldState('GroupIds');
    setClicked(false);
    app.user.current().then(({ loginUin }) => {
      let title = '';
      if (value === '1') {
        title = t('架构评估报告-{{loginUin}}', { loginUin });
      } else if (value === '2') {
        title = t('容量报告-{{loginUin}}', { loginUin });
      } else {
        title = t('云巡检报告-{{loginUin}}', { loginUin });
      }
      form.change('EmailTitle', title);
      form.change('ReportType', value);
    });
  };

  useEffect(() => {
    setTimeout(() => {
      if (changeSvgType !== 'delete') {
        if (values.ArchIds?.length > 0) {
          const pList = [];
          values.ArchIds.forEach((item) => {
            pList.push(getDescribeArchSvg(item));
          });
          Promise.all(pList).then((res) => {
            setSvgList(res);
          });
        } else {
          setSvgList([]);
        }
      }
    }, 0);
  }, [values.ArchIds, changeSvgType]);

  useEffect(() => {
    setTimeout(() => {
      getDescribeSubscriptionEmailList();
    }, 0);
  }, [limit, pageIndex]);

  useEffect(() => {
    if (subModalInfo.visible) {
      getProductsGroupsInfo();
      getDescribeReportArchList();
      setPageIndex(0);
      setIsCloseRefresh(false);
      getAllTagKeys();

      if (subModalInfo.subInfo) {
        if (subModalInfo.subInfo.Id !== undefined) {
          const obj: any = {};
          Object.keys(subModalInfo.subInfo).forEach((key) => {
            if (key === 'Tags') {
              setTagVal(subModalInfo.subInfo[key] === null ? [] : subModalInfo.subInfo[key]?.map((item) => ({
                attr: {
                  type: [
                    'multiple',
                    {
                      searchable: true,
                      all: true,
                    },
                  ],
                  key: item.TagKey,
                  name: item.TagKey,
                  values: item.TagValues.map((el) => ({
                    key: el,
                    name: el,
                  })),
                },
                values: item.TagValues.map((el) => ({
                  key: el,
                  name: el,
                })),
              })));
            }
            if (key === 'EmailInfo') {
              setSelectedKeys(subModalInfo.subInfo[key].EmailAddrs);
            } else if (key === 'TimeInfo') {
              obj.Period = subModalInfo.subInfo[key].Period;
              obj.Time = subModalInfo.subInfo[key].Time;
              obj.Monthly = subModalInfo.subInfo[key].Monthly.split(',')?.filter((item) => item !== '');
              obj.DaysOfWeek = subModalInfo.subInfo[key].DaysOfWeek.split(',')?.filter((item) => item !== '');
            } else if (subModalInfo.subInfo[key] === null) {
              if (key === 'Products') {
                obj[key] = products?.map((item) => item.Product);
              } else {
                obj[key] = [];
              }
            } else if (key === 'EmailTitle') {
              obj[key] = subModalInfo.subInfo[key]?.split(emailTitlePre)[1];
            } else if (key === 'RiskLevel') {
              obj[key] = subModalInfo.subInfo[key]?.toString();
            } else if (key === 'GroupIds') {
              obj[key] = subModalInfo.subInfo[key]?.map((item) => item.toString());
            } else if (key === 'Products') {
              if (obj[key]?.length === 0) {
                obj[key] = products?.map((item) => item.Product);
              } else {
                obj[key] = subModalInfo.subInfo[key];
              }
            } else {
              obj[key] = subModalInfo.subInfo[key];
            }
          });
          obj.Time = moment(obj.Time, 'HH:00');
          form.initialize({
            ...obj,
          });
          // if (subModalInfo.subInfo['Tags']?.length > 0 || subModalInfo.subInfo['IsHideCusName'] === true) {
          //   setOpenedSetting(true);
          // }
        } else {
          form.change('ArchIds', subModalInfo?.subInfo?.ArchIds ?? []);
          handleReportTypeChange(initialParams?.ReportType);
        }
      } else {
        handleReportTypeChange(values?.ReportType);
      }
    } else {
      Object.keys(initialParams).forEach((key) => {
        form.change(key, initialParams[key]);
      });
      setSelectedKeys([]);
      setSvgList([]);
      setChangeSvgType('select');
      setTagVal([]);
      setClicked(false);
      form.resetFieldState('ArchIds');
      form.resetFieldState('Name');
      form.resetFieldState('EmailTitle');
      form.resetFieldState('Products');
      form.initialize({ ...initialParams });
    }
  }, [subModalInfo]);

  useEffect(() => {
    if (tagKeys.length > 0) {
      getAllTagValues(tagKeys);
    }
  }, [tagKeys]);

  useEffect(() => {
    if (tagKeys.length > 0 && tagValues.length > 0) {
      setAttributes(tagKeys.map((item) => ({
        type: ['multiple', { searchable: true, all: true }],
        key: item,
        name: item,
        values: tagValues.filter((el) => el.TagKey === item).map((item) => ({
          key: item.TagValue,
          name: item.TagValue,
        })),
      })));
    }
  }, [tagKeys, tagValues]);

  return <Modal
    className="sub-report-modal"
    visible={subModalInfo.visible}
    caption={values.Id !== -1 ? t('编辑订阅') : t('新建订阅')}
    onClose={() => onClose(isCloseRefresh)}
  >
    {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
    <form onSubmit={handleSubmit}>
      <Form>
        <Modal.Body>
          <div className="sub-con-wrap">
            <div className="item">
              <div className="sub-item">
                <div className="t">{t('报告内容')}</div>
                <div className="form-con-wrap">
                  <Form.Item label={t('报告类型')}>
                    <Segment
                      {...reportTypeField.input}
                      options={
                        [
                          {
                            value: '1',
                            text: t('架构评估报告'),
                          },
                          {
                            value: '0',
                            text: t('云巡检报告'),
                          },
                          {
                            value: '2',
                            text: t('容量报告'),
                          },
                        ]
                      }
                      onChange={handleReportTypeChange}
                    />
                  </Form.Item>
                  {
                    values.ReportType === '1' || values.ReportType === '2'
                      ? <>
                        <Form.Item
                          className="archids-form-item"
                          status={getStatus(archIdsField.meta, validating)}
                          message={getStatus(archIdsField.meta, validating) === 'error' && archIdsField.meta.error}
                          label={t('架构名称')}
                          tips={t('支持多选订阅5张架构图')}
                        >
                          <SelectMultiple
                            value={values.ArchIds}
                            listWidth={286}
                            searchable
                            appearance="button"
                            onStagingValueChange={
                              (val) => {
                                archList?.forEach((item) => {
                                  if (val?.length > 4) {
                                    if (!val.includes(item.ArchId)) {
                                      // eslint-disable-next-line no-param-reassign
                                      item.disabled = true;
                                    }
                                  } else {
                                    // eslint-disable-next-line no-param-reassign
                                    item.disabled = false;
                                  }
                                });
                                setArchList(cloneDeep(archList));
                              }
                            }
                            options={
                              archList?.map((item) => ({
                                text: item.ArchName,
                                value: item.ArchId,
                                disabled: item.disabled === true,
                              }))
                            }
                            placeholder={t('请选择架构名称')}
                            onChange={
                              (val) => {
                                if (val.length > 5) {
                                  return;
                                }
                                setChangeSvgType('select');
                                form.change('ArchIds', val);
                              }
                            }
                          />
                        </Form.Item>
                        <div className="pic-wrap">
                          <div className="pic-outer">
                            {
                              svgList.map((item, i) => (
                                <div
                                    // eslint-disable-next-line react/no-array-index-key
                                  key={i}
                                  className={`pic-item ${svgList?.length < 3 ? 'pic-item-origin' : ''}`}
                                  style={
                                    {
                                      width: `${svgList?.length === 1 ? 287.5 : ((580 - (svgList.length - 1) * 5) / svgList.length)}px`,
                                    }
                                  }
                                >
                                  <div>
                                    <ArchSvgComponent detail={item?.svg || ''} />
                                  </div>
                                  <div
                                    className="pic-t"
                                    style={
                                      {
                                        maxWidth: `${(svgList?.length === 1 ? 287.5 : ((580 - (svgList.length - 1) * 5) / svgList.length)) - 2}px`,
                                      }
                                    }
                                    title={archList?.find((el) => el.ArchId === item.archId)?.ArchName}
                                  >
                                    {
                                        archList?.find((el) => el.ArchId === item.archId)?.ArchName
                                    }
                                  </div>
                                  <svg
                                    onClick={
                                      () => {
                                        const svgListNew = cloneDeep(svgList)?.filter((el, j) => j !== i);
                                        const archIds = cloneDeep(values.ArchIds)?.filter((el) => el !== item.archId);
                                        setSvgList(svgListNew);
                                        setChangeSvgType('delete');
                                        form.change('ArchIds', archIds);
                                      }
                                    }
                                    className="close-pic-icon"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 16 16"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path d="M8.00033 15.3334C12.0504 15.3334 15.3337 12.0502 15.3337 8.00008C15.3337 3.94999 12.0504 0.666748 8.00033 0.666748C3.95024 0.666748 0.666992 3.94999 0.666992 8.00008C0.666992 12.0502 3.95024 15.3334 8.00033 15.3334ZM5.87868 4.93572L8.00023 7.05727L10.1213 4.93618L11.0641 5.87898L8.94304 8.00008L11.0641 10.1212L10.1213 11.064L8.00023 8.94289L5.87868 11.0644L4.93587 10.1216L7.05742 8.00008L4.93587 5.87853L5.87868 4.93572Z" fill="black" fillOpacity="0.4" />
                                  </svg>
                                </div>
                              ))
                            }
                          </div>
                        </div>
                      </>
                      : <>
                        <Form.Item
                          status={getStatus(riskLevelField.meta, validating)}
                          message={getStatus(riskLevelField.meta, validating) === 'error' && riskLevelField.meta.error}
                          label={t('风险级别')}
                        >
                          <Radio.Group
                            {...riskLevelField.input}
                          >
                            <Radio name="0">{t('全部风险')}</Radio>
                            <Radio name="3">{t('只看高风险')}</Radio>
                            <Radio name="2">{t('只看中风险')}</Radio>
                          </Radio.Group>
                        </Form.Item>
                        <Form.Item
                          status={getStatus(groupIdslField.meta, validating)}
                          message={getStatus(groupIdslField.meta, validating) === 'error' && groupIdslField.meta.error}
                          label={t('风险类型')}
                          className="checkbox-group-wrap"
                        >
                          <Checkbox.Group
                            {...groupIdslField.input}
                          >
                            {
                              groups.map((item, i) => (
                                <Checkbox
                                  // eslint-disable-next-line react/no-array-index-key
                                  key={i}
                                  name={item.Id.toString()}
                                >
                                  {item.GroupName}
                                </Checkbox>
                              ))
                            }
                          </Checkbox.Group>
                        </Form.Item>
                        <Form.Item
                          className="product-form-item"
                          status={getStatus(productslField.meta, validating)}
                          message={getStatus(productslField.meta, validating) === 'error' && productslField.meta.error}
                          label={t('关注产品')}
                        >
                          <SelectMultiple
                            {...productslField.input}
                            searchable
                            appearance="button"
                            options={
                              products.map((item) => ({
                                text: item.Name,
                                value: item.Product,
                              }))
                            }
                            allOption={{
                              value: 'all',
                              text: t('全部产品'),
                            }}
                            placeholder={t('请选择关注产品')}
                          />
                        </Form.Item>
                        <Form.Item
                          status={getStatus(tagsField.meta, validating)}
                          message={getStatus(tagsField.meta, validating) === 'error' && tagsField.meta.error}
                          label={t('资源标签')}
                          className="resource-form-item"
                        >
                          <TagSearchBox
                            hideHelp
                            attributes={attributes}
                            minWidth={268}
                            value={tagVal}
                            onChange={
                              (val) => {
                                const valuesCopyFilter = val.filter((item) => !!item?.attr);
                                setTagVal(valuesCopyFilter);
                                form.change('Tags', valuesCopyFilter.map((item) => {
                                  const { key } = item.attr;
                                  return {
                                    TagKey: key,
                                    TagValues: item.values?.map((el) => el.key) || [],
                                  };
                                }));
                              }
                            }
                          />
                        </Form.Item>
                        <Form.Item
                          status={getStatus(isHideCusNameField.meta, validating)}
                          message={getStatus(isHideCusNameField.meta, validating) === 'error' && isHideCusNameField.meta.error}
                          label={t('报告配置')}
                          // className="resource-form-item"
                        >
                          <Checkbox {...isHideCusNameField.input}>
                            隐藏报告中用户名称
                          </Checkbox>
                        </Form.Item>
                      </>
                  }
                </div>
              </div>
              <div className="sub-item">
                <div className="t">{t('订阅信息')}</div>
                <div className="form-con-wrap">
                  <Form.Item
                    status={getStatus(nameField.meta, validating)}
                    message={getStatus(nameField.meta, validating) === 'error' && nameField.meta.error}
                    label={t('订阅名称')}
                  >
                    <Input {...nameField.input} placeholder={t('请输入订阅名称')} />
                  </Form.Item>
                  <Form.Item
                    className="email-t-form-item"
                    status={getStatus(emailTitleField.meta, validating)}
                    message={getStatus(emailTitleField.meta, validating) === 'error' && emailTitleField.meta.error}
                    label={t('邮件标题')}
                    tips={t('架构图多选时不支持自定义邮件标题')}
                  >
                    <InputAdornment before={t('腾讯云顾问')}>
                      <Input {...emailTitleField.input} />
                    </InputAdornment>
                  </Form.Item>
                  <Form.Item
                    label={t('发送频率')}
                  >
                    <Segment
                      {...periodField.input}
                      options={FREQUENCY_OPTS}
                    />
                  </Form.Item>
                  {values.Period === 'Weekly' && (
                    <Form.Item
                      status={getStatus(daysOfWeekField.meta, validating)}
                      message={getStatus(daysOfWeekField.meta, validating) === 'error' && daysOfWeekField.meta.error}
                      label={t('发送星期')}
                    >
                      <SelectMultiple
                        appearance="button"
                        options={WEEK_OPTS}
                        {...daysOfWeekField.input}
                      />
                    </Form.Item>
                  )}
                  {values.Period === 'Monthly' && (
                    <Form.Item
                      status={getStatus(monthlyField.meta, validating)}
                      message={getStatus(monthlyField.meta, validating) === 'error' && monthlyField.meta.error}
                      label={t('发送日期')}
                    >
                      <SelectMultiple
                        appearance="button"
                        options={MONTH_OPTS}
                        {...monthlyField.input}
                      />
                    </Form.Item>
                  )}
                  <Form.Item label={t('发送时间')}>
                    <TimePicker
                      {...timeField.input}
                      format="HH:00"
                      minuteStep={60}
                      overlayClassName="intlc-assessment-subscribe-selector subscribe-time-selector"
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
            <div className="item">
              <div className="t email-t">
                <div>{t('接收用户')}</div>
                {
                  location.pathname === subscribePath ? <div
                    className="email-manage-btn"
                    onClick={
                      () => {
                        onClose(tabVal);
                      }
                    }
                  >
                    <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0.666992 0H15.3337V12L0.666992 12V0ZM2.00033 1.33333V2.55298L8.00033 5.58631L14.0003 2.55298V1.33333H2.00033ZM14.0003 4.04702L8.00033 7.08035L2.00033 4.04702V10.6667L14.0003 10.6667V4.04702Z" fill="#0052D9" />
                    </svg>
                    {
                      t('邮箱管理')
                    }
                  </div> : <a className="email-manage-btn" href="/advisor/subscribe?tab=emailManagement">
                                                          <svg width="16" height="12" viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0.666992 0H15.3337V12L0.666992 12V0ZM2.00033 1.33333V2.55298L8.00033 5.58631L14.0003 2.55298V1.33333H2.00033ZM14.0003 4.04702L8.00033 7.08035L2.00033 4.04702V10.6667L14.0003 10.6667V4.04702Z" fill="#0052D9" />
                    </svg>
                                                          {
                      t('邮箱管理')
                    }
                                                        </a>
                }
              </div>
              <div className="email-con-wrap">
                <Table
                  className="email-tab"
                  records={emaiRecordlList}
                  rowDisabled={(item) => (selectedKeys.length > 50 && !selectedKeys.includes(item.email))}
                  recordKey="Email"
                  columns={columns}
                  addons={[
                    pageable({
                      recordCount: totalCount,
                      pageSize: limit,
                      onPagingChange: ({ pageIndex, pageSize }) => {
                        setPageIndex(pageIndex - 1);
                        setLimit(pageSize);
                      },
                      // stateTextVisible: false,
                      endJumpVisible: false,
                      pageSizeVisible: false,
                      stateText: t('已选择{{count}}条', {
                        count: selectedKeys.length,
                      }),
                    }),
                    selectable({
                      targetColumnKey: 'select',
                      value: selectedKeys,
                      onChange: (keys) => {
                        setSelectedKeys(keys);
                      },
                    }),
                  ]}
                  topTip={
                    (emailLoading
                        || emaiRecordlList?.length === 0)
                        && <StatusTip status={emailLoading ? 'loading' : 'empty'} />
                  }
                />
                <div
                  style={
                    {
                      marginTop: '-15px',
                      paddingBottom: '10px',
                    }
                  }
                  className="email-err-text"
                >
                  {
                    clicked && selectedKeys?.length === 0 ? t('请选择接收邮箱') : ''
                  }
                </div>
              </div>
            </div>
          </div>

        </Modal.Body>
        <Modal.Footer>
          <div>
            <Button
              loading={saveLoading}
              onClick={() => setClicked(true)}
              htmlType="submit"
              style={{ marginRight: 20 }}
              type="primary"
            >
              {t('确认')}
            </Button>
            <Button
              type="weak"
              onClick={(e) => {
                onClose(isCloseRefresh);
                e.stopPropagation();
                e.preventDefault();
              }}
            >
              {t('取消')}
            </Button>
          </div>
        </Modal.Footer>
      </Form>
    </form>
  </Modal>;
}
