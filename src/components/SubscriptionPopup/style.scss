:global {
  .subscribe-time-selector {
    .sdk-advisor-common-components-calendar {
      width: 170px;
    }
  
    .sdk-advisor-common-components-calendar__footer {
      padding-right: 0;
      padding-left: 0;
  
      button:nth-child(1) {
        margin-left: 0 !important;
      }
    }
  
    .sdk-advisor-common-components-justify-grid .sdk-advisor-common-components-justify-grid__col:first-child {
      text-align: center;
    }
  }
  
  .sub-report-modal {
    .sdk-advisor-common-components-dialog__inner {
      width: 1000px;
      padding: 0 15px 20px 15px;
  
      .sdk-advisor-common-components-dialog__footer {
        width: calc(100% + 30px);
        padding-top: 20px;
        padding-left: 15px;
        border-top: 1px solid #e7e7e7;
        margin-top: 0;
        margin-left: -15px;
      }
  
      .sdk-advisor-common-components-dialog__header {
        width: calc(100% + 30px);
        padding-top: 14px;
        padding-bottom: 14px;
        padding-left: 15px;
        border-bottom: 1px solid #e7e7e7;
        margin-bottom: 0;
        margin-left: -15px;
        line-height: 22px;
  
        .sdk-advisor-common-components-dialog__headertitle {
          margin-bottom: 0;
          font-size: 16px;
        }
  
        .sdk-advisor-common-components-btn--icon {
          top: 8px;
          right: 10px;
        }
      }
  
      .sdk-advisor-common-components-form .sdk-advisor-common-components-form__controls {
        padding-bottom: 15px;
      }
  
      .sub-con-wrap {
        display: flex;
        min-height: 450px;
  
        .item {
          &:nth-child(1) {
            width: 620px;
            border-right: 1px solid #e7e7e7;
          }
  
          &:nth-child(2) {
            width: 350px;
            box-sizing: border-box;
            padding: 10px 0 0 20px;
          }
  
          .sub-item {
            height: 295px;
            padding-top: 10px;
            
            &:nth-child(1) {
              position: relative;
              border-bottom: 1px solid transparent;
  
              &:after {
                position: absolute;
                bottom: 0;
                left: -15px;
                width: calc(100% + 15px);
                border-bottom: 1px solid #e7e7e7;
                content: '';
              }
            }
          }
        }
  
        .sdk-advisor-common-components-input-group {
          display: flex;
        }
  
        .t {
          margin-bottom: 15px;
          color: rgba(0, 0, 0, .60);
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
        }
        
        .email-t {
          display: flex;

          > div {
            flex-grow: 1;
            flex-shrink: 1;
          }

          .email-manage-btn {
            display: flex;
            flex: none;
            align-items: center;
            color: #0054e1;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;

            svg {
              margin-right: 5px;
            }
          }
        }
  
        .checkbox-group-wrap {
          .sdk-advisor-common-components-form__controls {
            padding-right: 0;
          }
  
          .sdk-advisor-common-components-form-check {
            &:last-child {
              margin-right: 0;
            }
          }
        }
  
        .sdk-advisor-common-components-search__inner {
          button {
            display: none;
          }
        }
  
        .sdk-advisor-common-components-input {
          width: 100%;
        }
  
        .sdk-advisor-common-components-dropdown-btn.sdk-advisor-common-components-dropdown__header {
          width: 249px;
        }
  
        .form-con-wrap {
          padding-left: 5px;
        }
      }
  
      .email-tab {
        border-radius: 6px 6px 0 0;
        border-bottom: 1px solid #dcdcdc;
  
        .sdk-advisor-common-components-table__header {
          display: none;
        }
  
        .sdk-advisor-common-components-table__body {
          border-bottom: none;
        }
  
        .sdk-advisor-common-components-table__box tbody tr.is-selected {
          background-color: transparent;
        }
  
        .sdk-advisor-common-components-table__box tbody tr:hover {
          background-color: transparent;
        }
        
        .email-line {
          display: flex;
          line-height: 22px;

          .label-t {
            color: rgba(0, 0, 0, .40);
          }
        }

        .sdk-advisor-common-components-table__box td {
          padding-top: 10px;
          padding-bottom: 10px;
        }
      }
  
      .sdk-advisor-common-components-pagination__inputpagenum {
        width: 35px !important;
      }
  
      .email-con-wrap {
        width: 330px;
        border: 1px solid #dcdcdc;
        border-radius: 6px;
        margin-top: 13px;
        margin-bottom: 15px;
      }
  
      .resource-form-item {
        position: relative;
        z-index: 3;
  
        .ruyi-icon-error-circle {
          position: absolute;
          top: 12px;
          right: 5px;
        }
      }
  
      .email-t-form-item {
        position: relative;
  
        .ruyi-icon-error-circle {
          position: absolute;
          top: 0px;
          right: 0px;
        }
      }
  
      .archids-form-item {}
  
      .sdk-advisor-common-components-form .sdk-advisor-common-components-form__controls.is-error {
        padding-bottom: 0;
      }
  
      .sdk-advisor-common-components-form__help-text {
        margin-top: 5px;
        margin-bottom: 5px;
        line-height: 12px;
      }

      .email-err-text {
        margin-top: -15px;
        margin-left: 10px;
        color: #e54545;
      }
  
      .sdk-advisor-common-components-form .sdk-advisor-common-components-form__controls>.sdk-advisor-common-components-icon-valid {
        display: none;
      }
  
      .sdk-advisor-common-components-search.sdk-advisor-common-components-search--tags.is-active {
        width: 286px;
      }
  
      .product-form-item {
        .sdk-advisor-common-components-dropdown-btn.sdk-advisor-common-components-dropdown__header {
          width: 268px;
        }
      }
  
      .pic-wrap {
        position: relative;
        display: flex;
        overflow: auto;
        width: 592px;
        height: 157px;
        box-sizing: border-box;
        border: 1px solid #dcdcdc;
        border-radius: 3px;
        overflow-x: hidden;
        overflow-y: auto;
    
        &::-webkit-scrollbar {
          width: 5px;
          height: 6px;
        }
    
        &::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background: #888;
        }
    
        &::-webkit-scrollbar-track {
          border-radius: 4px;
        }
    
        .pic-outer {
          position: absolute;
          display: flex;
          width: 590px;
          min-height: 150px;
          box-sizing: border-box;
          flex-wrap: wrap;
          padding: 5px;
        }
    
        .pic-item {
          position: relative;
          width: 190px;
          height: 120px;
          box-sizing: border-box;
          border: 1px solid #eff4ff;
          margin-left: 5px;

          &:nth-child(1) {
            margin-left: 0px;
          }

          > div {
            height: 120px;

            > svg {
              overflow: auto !important;
              width: 100%;
              height: 120px;

              svg:not(:root) {
                overflow: visible;
              }
            }
          }

          .pic-t {
            overflow: hidden;
            max-width: 188px;
            height: 20px;
            color: rgba(0, 0, 0, .4);
            line-height: 20px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .close-pic-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
          }
        }

        .pic-item-origin {
          width: 287px;

          .pic-t {
            max-width: 287px;
          }
        }
      }
  
      .open-setting-wrap {
        display: inline-flex;
        align-items: center;
        color: #0052d9;
        cursor: pointer;
  
        &.opened-setting-wrap {
          svg {
            transform: rotate(180deg);
          }
        }
  
        svg {
          margin-right: 5px;
          transition: all .3s ease-in;
        }
      }
  
      .more-setting-inspect-wrap {
        position: relative;
        z-index: -1;
        padding-left: 20px;
        border-radius: 0px 0px 10px 10px;
        margin-top: 5px;
        margin-left: -20px;
        background-color: #fff;
        box-shadow: 0 3px 2px -1px rgba(0, 0, 0, .1);
        opacity: 0;
      }
  
      .more-setting-inspect-wrap-transition {
        z-index: 2;
        opacity: 1;
        transition: opacity .3s ease-in;
      }
    }
  }
  
}