/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
import './i18n';

// 导入依赖
import { app } from '@tencent/tea-app';
import SubscriptionPopup from './components/SubscriptionPopup';
import ArchSvgComponent from './components/ArchSvgComponent';

import '@tencent/tea-component/dist/tea.css';

// 注册 SDK 入口，提供 SDK 工厂方法
app.sdk.register('advisor-common-components-sdk', () => ({
  SubscriptionPopup,
  ArchSvgComponent,
}));
