export interface SubscriptionInfos {
  SubscriptionInfos: Array<SubscriptionInfo>;
  [propName: string]: any;
}

export interface SubscriptionInfo {
  Id: number;
  IsSubscribed: boolean;
  Language: string;
  Name?: string;
  TplId?: number;
  TplName?: string;
  TimeInfo: SubscriptionTimeInfo;
  EmailInfo: SubscriptionEmailInfo;
  EmailTitle: string;
  ReportType: number;
  ArchIds?: Array<string>;
  RiskLevel?: number;
  GroupIds?: Array<number>;
  Products?: string;
  Tags?: Array<{
    TagKey: string;
    TagValues: Array<string>;
  }>;
  RiskThemes?: Array<string>; // 新增风险主题字段
}

interface SubscriptionTimeInfo {
  Period: string;
  DayOfWeek?: number;
  Time: string;
  DaysOfWeek: string;
}

interface SubscriptionEmailInfo {
  EmailAddrs: Array<string>;
}

export interface Languages {
  Languages: Array<string>;
  [propName: string]: any;
}

export interface Filters {
  Filters: Array<Filter>;
  [propName: string]: any;
}

interface Filter {
  Name: string;
  Values: Array<string>;
}
export interface SubscriptionParams {
  Offset?: number;
  Limit?: number;
  Filters?: Array<Filter>;
}
export interface SubscriptionTplParams {
  GroupIds?: Array<number>;
  Id?: number;
  Products?: Array<string>;
  Name?: string;
  TplName?: string;
  Tags?: Array<any>;
  ReportType?: string;
  ArchId?: string;
  ArchName?: string;
}
export interface SubscriptionTpl {
  SubscriptionTemplateInfo: SubscriptionTplParams;
}

export interface SubscriptionTplTag {
  TagKey: string;
  TagValues: Array<string>;
  editingKey?: boolean;
  editingValues?: boolean;
}

export interface IDescribeRiskManageSubjectListParams {
  ArchId: string;
  Limit: number;
  Offset: number;
}
