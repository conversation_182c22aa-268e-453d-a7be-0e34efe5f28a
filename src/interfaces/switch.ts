export interface IngoreStrategy {
  StrategyIds: Array<number>;
  [propName: string]: any;
}

export interface Strategy {
  strategyId: number;
  type: string;
  productShortName: string;
  product: string;
  strategyName: string;
  strategyDescribe: string;
  isAssessing: boolean;
  IsSupportCustom: boolean;
}

export interface Filters {
  Filters: Array<Filter>;
  [propName: string]: any;
}

interface Filter {
  Name: string;
  Values: Array<string>;
}

export interface GlobalTags {
  TotalCount: number;
  Tags: Array<Tags>;
  [propName: string]: any;
}

export interface Tags {
  TagKey: string;
  TagValues: Array<string>;
}

// DescribeTagKeys 接口返回数据类型
export interface ResTagKeys {
  TotalCount: number;
  Tags: Array<string>;
  [propName: string]: any;
}

export interface TagSon {
  TagKey: string;
  TagValue: string;
}
// DescribeTagValues 接口返回数据类型
export interface ResTagValues {
  TotalCount: number;
  Tags: Array<TagSon>;
  [propName: string]: any;
}

export interface PolicyItem {
  MetricName: string;
  MetricNameAlias: string;
  Factor: string;
  Days: number;
  MaxPercent: number;
  Value: number;
  Unit: string;
}
export interface ConditionItem {
  StrategyId: number;
  ConditionId: number;
  RiskLevel: number;
  LogicalOperators: string;
  Policy: Array<PolicyItem>;
}

export interface Condition {
  Condition: Array<ConditionItem>;
  [propName: string]: any;
}

export interface ModifyIgnoreStatusParams {
  Operate: string;
  IgnoreType: number;
  IgnoreInfos: Array<{
    Name: string;
    Values: Array<string>;
  }>;
}

export interface DescribeIgnoredInstancesParams {
  Limit: number;
  Offset: number;
  Filters: Array<{
    Name: string;
    Values: Array<string>;
  }>;
}

export interface DescribeFuzzyIgnoreInfoParams {
  Limit: number;
  Offset: number;
}
