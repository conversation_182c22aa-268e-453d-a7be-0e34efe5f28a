/**
 * 一些公用的常量
 */

import { t } from '@tea/app/i18n';

export const FREQUENCY_OPTS = [
  { value: 'Daily', text: t('每天') },
  { value: 'Weekly', text: t('每周') },
  { value: 'Monthly', text: t('每月') },
];

export const WEEK_OPTS = [
  { value: '1', text: t('星期一') },
  { value: '2', text: t('星期二') },
  { value: '3', text: t('星期三') },
  { value: '4', text: t('星期四') },
  { value: '5', text: t('星期五') },
  { value: '6', text: t('星期六') },
  { value: '7', text: t('星期天') },
];

export const MONTH_OPTS = [
  { value: '1', text: t('1号') },
  { value: '2', text: t('2号') },
  { value: '3', text: t('3号') },
  { value: '4', text: t('4号') },
  { value: '5', text: t('5号') },
  { value: '6', text: t('6号') },
  { value: '7', text: t('7号') },
  { value: '8', text: t('8号') },
  { value: '9', text: t('9号') },
  { value: '10', text: t('10号') },
  { value: '11', text: t('11号') },
  { value: '12', text: t('12号') },
  { value: '13', text: t('13号') },
  { value: '14', text: t('14号') },
  { value: '15', text: t('15号') },
  { value: '16', text: t('16号') },
  { value: '17', text: t('17号') },
  { value: '18', text: t('18号') },
  { value: '19', text: t('19号') },
  { value: '20', text: t('20号') },
  { value: '21', text: t('21号') },
  { value: '22', text: t('22号') },
  { value: '23', text: t('23号') },
  { value: '24', text: t('24号') },
  { value: '25', text: t('25号') },
  { value: '26', text: t('26号') },
  { value: '27', text: t('27号') },
  { value: '28', text: t('28号') },
  { value: '29', text: t('29号') },
  { value: '30', text: t('30号') },
  { value: '31', text: t('31号') },
];

// 风险主题选项
export const RISK_THEME_OPTS = [
  { value: 'security', text: t('安全风险') },
  { value: 'performance', text: t('性能风险') },
  { value: 'cost', text: t('成本风险') },
  { value: 'reliability', text: t('可靠性风险') },
  { value: 'compliance', text: t('合规风险') },
  { value: 'operation', text: t('运维风险') },
];

export const DAYOFWEEKS_ONE = [t('每天'), t('每周一'), t('每周二'), t('每周三'), t('每周四'), t('每周五'), t('每周六'), t('每周日')];

export const DAYOFWEEKS_TWO = [t('每天'), t('周一'), t('周二'), t('周三'), t('周四'), t('周五'), t('周六'), t('周日')];
