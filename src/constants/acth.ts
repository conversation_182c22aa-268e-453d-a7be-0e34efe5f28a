import { t } from '@tea/app/i18n';

export enum TypeEnum {
  TEMPLATE = 'template', // 模板
  NEW_TEMPLATE = 'newTemplate', // 新建模板
  ARCHITECTURE = 'architecture', // 已有架构
  NEW_ARCHITECTURE = 'newArchitecture', // 新建架构
  NEW_ARCHITECTURE_FROM_TEMPLATE = 'newArchitectureFromTemplate', // 新建架构
  HISTORY_ARCHITECTURE = 'historyArchitecture', // 架构历史版本
}

export enum SaveMode {
  UPDATE_ARCH = 'updateArch', // 更新架构
  SAVE_AND_ADD_VERSION = 'saveAndAddVersion', //  保存并添加版本
  SAVE_AS_NEW_APP = 'saveAsNewApp', //  保存为新的应用
  SAVE_AS_NEW_TEMPLATE = 'saveAsNewTemplate', //  保存为新的模板
  UPDATE_TEMPLATE = 'updateTemplate', //  更新模板
}

export enum EditorViewModeEnum {
  THREE_D = '3d',
  TWO_D = '2d',
}

export enum ModeEnum {
  THREE_D = 'SIGMA_GRAPH_MODE_3D',
  TWO_D = 'SIGMA_GRAPH_MODE_2D',
}

// 初始画布比例
export const INIT_SCALE = 1;

export const PRODUCT_SHARP_SIZE = {
  SHARP_2D_WIDTH: 90,
  SHARP_2D_HEIGHT: 90,
  SHARP_3D_WIDTH: 128,
  SHARP_3D_HEIGHT: 74,
};

export const BLANK_DETAIL = '{}';
export const BLANK_SVG = '<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200"><defs><pattern x="0" y="37" patternUnits="userSpaceOnUse" id="entire-grid" width="128" height="74"><path d="M 64 0 L 128 37 64 74 0 37 z" stroke="#dcdcdc" stroke-width="1" fill="none"></path></pattern><pattern x="0" y="37" patternUnits="userSpaceOnUse" id="quarter-grid" width="128" height="74"><path d="M 0 0 L 128 74" stroke="#eeeeee" stroke-width="1"></path><path d="M 128 0 L 0 74" stroke="#eeeeee" stroke-width="1"></path></pattern></defs><g><g pointer-events="none" g-name="sence"><g><polygon points="-2000 -2000, 2000 -2000, 2000 2000, -2000 2000" fill="url(#quarter-grid)" class="quarter-grid"></polygon></g></g><g pointer-events="none" g-name="grid"><g><polygon points="-2000 -2000, 2000 -2000, 2000 2000, -2000 2000" fill="url(#entire-grid)" class="entire-grid"></polygon></g></g><g pointer-events="auto" g-name="gNetwork"></g><g pointer-events="auto" g-name="gRect"></g><g pointer-events="auto" g-name="gLine"></g><g pointer-events="auto" g-name="gCircle"></g><g pointer-events="auto" g-name="gProduct"></g><g pointer-events="auto" g-name="gImage"></g><g pointer-events="auto" g-name="gIcon"></g><g pointer-events="auto" g-name="gText"></g><g pointer-events="auto" g-name="gCache"></g></g></svg>';

export const NEW_VERSION = '1.0.0';

export const NO_LABEL_SHAPE_TYPE = ['SIGMA_LINE_SHAPE'];

export enum ArchShareLevelEnum {
  NotShare = 0,
  ReadOnly = 1,
  Editable = 2,
  RunnablePlugins = 3,
}

export const ArchShareLevelMap = {
  [t('可编辑')]: ArchShareLevelEnum.Editable,
  [t('只读')]: ArchShareLevelEnum.ReadOnly,
  [t('可执行插件')]: ArchShareLevelEnum.RunnablePlugins,
};

// 不展示的shape
export const NO_SHOW_SHAPE = [];

export const DRAG_PRODUCT_DATA_KEY = 'DRAG_PRODUCT_DATA_KEY';

export const DRAG_GROUP_DATA_KEY = 'DRAG_GROUP_DATA_KEY';

export enum DragBox {
  THREE_D_BOX_WIDTH = 152,
  THREE_D_BOX_HEIGHT = 121,
  TWO_D_BOX_WIDTH = 74,
  TWO_D_BOX_HEIGHT = 70,
}

export const TKE_CLUSTER = 'TKE GROUP';
export const TKE_NAME_LIST = ['TKE GROUP', 'TKE Deployment', 'TKE StatefulSet', 'TKE DaemonSet', 'TKE Ingress', 'TKE Service'];

export enum CreateSource {
  CloudArch = 'CloudArch',
  CloudInspection = 'CloudInspection',
  MSP = 'MSP',
  CloudInspectionTag = 'CloudInspectionTag',
  Chaos = 'Chaos',
}

export const sourceMap = {
  'cfg-advisor-sdk': CreateSource.Chaos,
};

export const SINGLE_SHAPE_LIST = ['Cloud Monitoring', 'CSC', 'CAM'];

export const BIND_TYPE_MAP = {
  instance: t('资源选择'),
  instances: t('资源选择'),
  tag: t('标签绑定'),
  tagBinding: t('标签绑定'),
  fuzzy: t('模糊绑定'),
  fuzzyBinding: t('模糊绑定'),
  discover: t('自动发现'),
};
