/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable import/no-extraneous-dependencies */
const path = require('path');
const StylelintPlugin = require('stylelint-webpack-plugin');
const ESLintPlugin = require('eslint-webpack-plugin');

/**
 * Tea 项目配置
 * @type {import("@tencent/tea-types/config").Configuration}
 */
module.exports = {
  command: {
    dev: {
      port: 8321,
      https: true,
    },
  },
  buffet: {
    productId: 1336,
    zh: [
      {
        site: 1,
        route: 'advisor-common-components-sdk',
      },
    ],
  },

  webpack: (config, { MiniCssExtractPlugin }) => {
    const devMode = config.mode === 'development';

    // @ts-ignore
    config.resolveLoader.modules.push(path.resolve(__dirname, 'node_modules'));
    config.module.rules.push({
      test: /\.(sa|sc)ss$/,
      use: [
        {
          loader: MiniCssExtractPlugin.loader,
        },
        {
          loader: 'css-loader',
          options: {
            modules: {
              localIdentName: '[name]__[local]--[hash:base64:5]',
            },
          },
        },
        'sass-loader',

      ],
    }, {
      test: /\.js$/,
      exclude: /node_modules(?!\/@svgdotjs\/svg\.js)/, // 修改这里，不排除@svgdotjs/svg.js库
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-env'],
        },
      },
    });

    // 修改原规则，对 /svg-component 目录下的svg文件不转换为base64
    const svgRule = config.module.rules.find((rule) => rule.test.toString() === /\.svg$/.toString());
    svgRule.exclude = /svg-component\/.*\.svg$/;

    config.module.rules.push({
      test: /svg-component\/.*\.svg$/,
      use: ['@svgr/webpack'],
    });

    config.plugins.push(new StylelintPlugin({
      configFile: './stylelint.config.js',
      context: 'src', // 指定CSS/SCSS文件所在的目录
      files: '**/*.(s(c|a)ss|css)', // 指定要检查的文件扩展名
      failOnError: true, // 如果希望webpack在Stylelint错误上构建失败，则设置为True
      failOnWarning: false, // 如果希望webpack在Stylelint上生成警告失败，则设置为True
      quiet: false, // 设置为TRUE可在控制台中取消Stylelint警告
      quietDeprecationWarnings: true, // 关闭过期规则警告
      fix: true, // 自动修复
    }));
    // @ts-ignore
    config.plugins.push(new ESLintPlugin({
      extensions: ['ts', 'tsx', 'js', 'jsx'],
      exclude: ['node_modules', 'i18n'],
    }));

    return config;
  },
};
