{"name": "@tencent/tea-sdk-advisor-common-components", "version": "1.0.0", "description": "The advisor-data-subscription-sdk tea sdk for qcloud console", "main": "src/sdk.js", "private": true, "scripts": {"dev": "tea dev", "scan": "tea scan", "build": "tea build", "deploy": "tea commit", "build-types": "rm -rf dts/types && npx tsc -d --emitDeclarationOnly --skipLibCheck --declarationDir dts/types", "publish-types": "cd dts && npm version patch && tnpm publish", "eslint": "npx eslint src --ext .ts,.tsx", "fix": "npx eslint src --ext .ts,.tsx --fix", "fix-style": "stylelint src/**/*.{scss,css} --fix --cache --quiet-deprecation-warnings"}, "keywords": ["tea", "sdk", "advisor-data-subscription-sdk"], "engines": {"typescript": ">3.3"}, "resolutions": {"braces": "3.0.3"}, "license": "UNLICENSED", "dependencies": {"@reduxjs/toolkit": "^1.9.7", "@tencent/tea-app": "^2.1.25", "@tencent/tea-chart-v3": "0.1.3", "@tencent/tea-component": "^2.8.3", "@tencent/tea-cli": "^2.6.9", "axios": "^1.11.0", "classnames": "^2.5.1", "final-form": "4.20.9", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^16.8.3", "react-dom": "^16.8.3", "react-final-form-hooks": "^2.0.2", "react-redux": "^8.0.5"}, "devDependencies": {"@babel/eslint-parser": "^7.15.7", "@svgr/webpack": "^5.1.0", "@tencent/eslint-config-tencent": "^1.0.4", "@tencent/eslint-plugin-tea-i18n": "^0.1.1", "@tencent/stylelint-config-tencent": "^1.0.10", "@tencent/tea-scripts": "2.1.28-beta.0", "@tencent/tea-types": "^0.1.0", "@types/js-cookie": "^3.0.6", "@types/qs": "^6.9.10", "@types/react": "^16.8.4", "@types/react-dom": "^16.8.2", "@types/react-router-dom": "~5.1.8", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "eslint": "^7.8.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-webpack-plugin": "2.5.4", "postcss": "^8.4.31", "postcss-scss": "^4.0.9", "sass": "^1.69.5", "sass-loader": "^10.3.2", "stylelint": "^15.11.0", "stylelint-config-idiomatic-order": "^9.0.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^5.3.1", "stylelint-webpack-plugin": "^4.1.1", "typescript": "^4.3.5"}}